import { useState, useEffect, useCallback } from "react";
// Components
import { CookieConsentBanner } from "./components/CookieConsentBanner";
import { WelcomeScreen } from "./components/WelcomeScreen";
import { Header } from "./components/Header/Header";
import MainView from "./components/views/MainView";
import PlayView from "./components/views/PlayView";
import RulesView from "./components/views/RulesView";
import LivesView from "./components/views/LivesView";
import CluesView from "./components/views/CluesView";
import { LoadBlock } from "microapps";
// Contexts
import { useAppContext } from "./contexts/AppContext";
import { useEnygmaGame } from "./contexts/EnygmaGameContext";
import { useGameOrchestrator } from "./contexts/GameOrchestratorContext";
// Types
import type { GameMode } from "./services/AIService";
// Styles
import "./App.scss";

type ViewMode = "main" | "play" | "rules" | "lives" | "clues";
type AppState = "loading" | "consent" | "welcome" | "ready"; // 🆕 Estados de la app

function App() {
  const { isInitialized, errors } = useAppContext();
  const [appState, setAppState] = useState<AppState>("loading");
  const [currentView, setCurrentView] = useState<ViewMode>("main");
  const [hasPlayedWelcome, setHasPlayedWelcome] = useState<boolean>(false);
  const [isStartingGame, setIsStartingGame] = useState<boolean>(false);

  // Contexts
  const {} = useEnygmaGame();
  const { startGameFlow } = useGameOrchestrator();

  // ========== EFECTOS ==========
  useEffect(() => {
    if (isInitialized) {
      // Verificar estado inicial de la app
      const hasConsent = localStorage.getItem("enygma_analytics_consent");

      if (!hasConsent) {
        setAppState("consent");
      } else {
        setAppState("welcome");
      }
    }
  }, [isInitialized]);

  useEffect(() => {
    if (errors.length > 0) {
      console.warn("⚠️ App: Errores detectados:", errors);
    }
  }, [errors]);

  // ========== HANDLERS ==========
  const handleAudioActivated = useCallback(() => {
    setAppState("welcome");
  }, []);

  const handleConsentGiven = useCallback(() => {
    setAppState("welcome");
  }, []);

  const handleWelcomeComplete = useCallback(() => {
    setAppState("ready");
    setHasPlayedWelcome(true);
  }, []);

  const handleStartGame = async (mode: GameMode) => {
    setIsStartingGame(true);

    try {
      if (!hasPlayedWelcome) {
        setHasPlayedWelcome(true);
      }

      await startGameFlow(mode);
      setCurrentView("play");
    } catch (error) {
      console.error("Error al iniciar el juego:", error);
    } finally {
      setIsStartingGame(false);
    }
  };

  const handleToggleSound = () => {
    console.log("Toggle sound");
  };

  const handleGoHome = () => {
    console.log("Go to home");
  };

  const handleShowRules = () => {
    setCurrentView("rules");
  };

  const handleShowLives = () => {
    setCurrentView("lives");
  };

  const handleShowClues = () => {
    setCurrentView("clues");
  };

  const handleExistGame = () => {
    setCurrentView("main");
  };

  const handleBackToMain = () => {
    setCurrentView("main");
  };

  // ========== RENDERIZADO CONDICIONAL ==========
  if (appState === "loading") {
    return (
      <div className="loader-container">
        <div className="loader">
          <LoadBlock interval={6000} text="Cargando..." />
        </div>
      </div>
    );
  }

  if (appState === "consent") {
    return (
      <div className="App">
        <div className="game-container">
          <img
            src="assets/game/background.png"
            alt="Background"
            className="background"
          />

          <CookieConsentBanner
            onAudioActivated={handleAudioActivated}
            onConsentGiven={handleConsentGiven}
          />
        </div>
      </div>
    );
  }

  if (appState === "welcome") {
    return (
      <div className="App">
        <div className="game-container">
          <img
            src="assets/game/background.png"
            alt="Background"
            className="background"
          />

          <WelcomeScreen onGameReady={handleWelcomeComplete} />
        </div>
      </div>
    );
  }

  // Ready state - aplicación principal
  const renderContent = () => {
    switch (currentView) {
      case "main":
        return (
          <MainView
            handleStartGame={handleStartGame}
            handleShowRules={handleShowRules}
            isStartingGame={isStartingGame}
            isReady={true} // En estado "ready", siempre está listo
          />
        );
      case "play":
        return (
          <PlayView
            handleShowLives={handleShowLives}
            handleShowClues={handleShowClues}
            handleExistGame={handleExistGame}
          />
        );
      case "rules":
        return <RulesView isOpen={true} onClose={handleBackToMain} />;
      case "lives":
        return <LivesView />;
      case "clues":
        return <CluesView />;
      default:
        return <div className="content"></div>;
    }
  };

  return (
    <div className="App">
      <div className="game-container">
        <img
          src="assets/game/background.png"
          alt="Background"
          className="background"
        />

        <div className="board">
          <Header
            currentView={currentView}
            onBackToMain={handleBackToMain}
            onToggleSound={handleToggleSound}
            onGoHome={handleGoHome}
            showBackButton={currentView !== "main" && currentView !== "play"}
          />

          {renderContent()}
        </div>
      </div>
    </div>
  );
}

export default App;
